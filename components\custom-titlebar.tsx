"use client"

import React, { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Minus, X, Maximize2, Minimize2 } from "lucide-react"
import { useTauri } from "@/hooks/use-tauri"

interface CustomTitlebarProps {
  title?: string
  className?: string
}

export function CustomTitlebar({ title = "简单笔记", className = "" }: CustomTitlebarProps) {
  const { isTauri, isLoading } = useTauri()
  const [isMaximized, setIsMaximized] = useState(false)

  useEffect(() => {
    // 如果在 Tauri 环境中，检查窗口状态并监听变化
    const setupWindowState = async () => {
      if (!isTauri) return

      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window")
        const currentWindow = getCurrentWindow()

        // 检查初始状态
        const maximized = await currentWindow.isMaximized()
        setIsMaximized(maximized)

        // 监听窗口状态变化
        const unlisten = await currentWindow.onResized(() => {
          // 当窗口大小改变时，重新检查最大化状态
          currentWindow.isMaximized().then(setIsMaximized)
        })

        return unlisten
      } catch (error) {
        console.error("Failed to setup window state:", error)
      }
    }

    let cleanup: (() => void) | undefined

    setupWindowState().then((unlisten) => {
      cleanup = unlisten
    })

    return () => {
      if (cleanup) {
        cleanup()
      }
    }
  }, [isTauri])

  // 如果正在加载或不在 Tauri 环境中，不渲染标题栏
  if (isLoading || !isTauri) {
    return null
  }

  const handleMinimize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      const currentWindow = getCurrentWindow()
      await currentWindow.minimize()
    } catch (error) {
      console.error("Failed to minimize window:", error)
    }
  }

  const handleMaximize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      const currentWindow = getCurrentWindow()
      await currentWindow.toggleMaximize()
      setIsMaximized(!isMaximized)
    } catch (error) {
      console.error("Failed to maximize window:", error)
    }
  }

  const handleClose = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      const currentWindow = getCurrentWindow()
      await currentWindow.close()
    } catch (error) {
      console.error("Failed to close window:", error)
    }
  }

  const handleDragStart = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      const currentWindow = getCurrentWindow()
      await currentWindow.startDragging()
    } catch (error) {
      console.error("Failed to start dragging:", error)
    }
  }

  return (
    <div
      className={`
        flex items-center justify-between h-10 bg-gray-50 dark:bg-background
        border-b border-gray-200 dark:border-border
        select-none relative z-[9999] backdrop-blur-sm
        ${className}
      `}
      style={{ WebkitAppRegion: "no-drag" } as React.CSSProperties}
    >
      {/* 可拖拽区域 */}
      <div
        className="flex-1 h-full flex items-center px-4 cursor-move"
        onMouseDown={handleDragStart}
        style={{ WebkitAppRegion: "drag" } as React.CSSProperties}
      >
        <div className="flex items-center gap-3">
          {/* 应用图标 */}
          <div className="w-5 h-5 bg-gradient-to-br from-gray-700 to-gray-900 dark:from-gray-600 dark:to-gray-800 rounded-md flex items-center justify-center shadow-sm">
            <span className="text-white text-xs">📝</span>
          </div>
          {/* 标题 */}
          <span className="text-sm font-semibold text-gray-900 dark:text-foreground truncate">
            {title}
          </span>
        </div>
      </div>

      {/* 窗口控制按钮 */}
      <div className="flex items-center h-full" style={{ WebkitAppRegion: "no-drag" } as React.CSSProperties}>
        {/* 最小化按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMinimize}
          className="h-10 w-12 rounded-none hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors duration-200 group"
          title="最小化"
        >
          <Minus className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200" />
        </Button>

        {/* 最大化/还原按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMaximize}
          className="h-10 w-12 rounded-none hover:bg-gray-200 dark:hover:bg-gray-800 transition-colors duration-200 group"
          title={isMaximized ? "还原" : "最大化"}
        >
          {isMaximized ? (
            <Minimize2 className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200" />
          ) : (
            <Maximize2 className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200" />
          )}
        </Button>

        {/* 关闭按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-10 w-12 rounded-none hover:bg-red-500 transition-colors duration-200 group"
          title="关闭"
        >
          <X className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 group-hover:text-white" />
        </Button>
      </div>
    </div>
  )
}
