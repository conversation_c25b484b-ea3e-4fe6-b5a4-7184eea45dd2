#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // 获取主窗口
      let window = app.get_webview_window("main").unwrap();

      // 监听窗口事件
      window.on_window_event(move |event| {
        match event {
          tauri::WindowEvent::Resized(_) => {
            // 窗口大小改变时的处理
          }
          tauri::WindowEvent::Moved(_) => {
            // 窗口移动时的处理
          }
          tauri::WindowEvent::CloseRequested { .. } => {
            // 窗口关闭请求时的处理
          }
          _ => {}
        }
      });

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
