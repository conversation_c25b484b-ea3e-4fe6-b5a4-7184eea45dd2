{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17690913698614214024, "build_script_build", false, 10608357784032086292], [12092653563678505622, "build_script_build", false, 18423637238081904029], [8324462083842905811, "build_script_build", false, 15237950677421070648]], "local": [{"RerunIfChanged": {"output": "debug\\build\\jdnotes-f8d5de7503eac8dc\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}