"use client"

import { useState, useEffect } from "react"

export function useTauri() {
  const [isTauri, setIsTauri] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkTauri = async () => {
      try {
        // 检查是否在 Tauri 环境中
        if (typeof window !== "undefined") {
          // @ts-ignore
          const hasTauri = window.__TAURI__ !== undefined
          setIsTauri(hasTauri)
        }
      } catch (error) {
        console.log("Not in Tauri environment:", error)
        setIsTauri(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkTauri()
  }, [])

  return { isTauri, isLoading }
}
