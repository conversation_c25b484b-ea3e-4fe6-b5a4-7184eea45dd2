"use client"

import React from "react"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import {
  Search,
  Plus,
  Trash2,
  Edit3,
  Calendar,
  Hash,
  Menu,
  Star,
  Tag,
  Filter,
  Moon,
  Sun,
  ArrowUpDown,
  Clock,
  ListOrderedIcon as AlphabeticalIcon,
  Heart,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>,
  <PERSON>,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SimpleTitlebar } from "@/components/simple-titlebar"
import { TauriDebug } from "@/components/tauri-debug"

interface Note {
  id: string
  title: string
  content: string
  tags: string[]
  isFavorite: boolean
  createdAt: Date
  updatedAt: Date
}

type SortOption = "updated" | "created" | "title" | "favorite"

interface DeleteConfirmation {
  isOpen: boolean
  noteId: string
  noteTitle: string
}

export default function NotesApp() {
  const [notes, setNotes] = useState<Note[]>([])
  const [selectedNote, setSelectedNote] = useState<Note | null>(null)
  const [selectedTag, setSelectedTag] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [searchInput, setSearchInput] = useState("") // 用于输入框显示
  const [sortBy, setSortBy] = useState<SortOption>("updated")
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState("")
  const [editContent, setEditContent] = useState("")
  const [editTags, setEditTags] = useState("")
  const [isMobile, setIsMobile] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null)
  const [theme, setTheme] = useState<"light" | "dark">("light")
  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({
    isOpen: false,
    noteId: "",
    noteTitle: "",
  })
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  // 搜索防抖
  const debouncedSearch = useMemo(() => {
    const debounce = (func: (value: string) => void, delay: number) => {
      let timeoutId: NodeJS.Timeout
      return (value: string) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => func(value), delay)
      }
    }
    return debounce((value: string) => setSearchTerm(value), 300)
  }, [])

  // 处理搜索输入
  const handleSearchInput = (value: string) => {
    setSearchInput(value)
    debouncedSearch(value)
  }

  // 主题管理
  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as "light" | "dark" | null
    const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
    const initialTheme = savedTheme || systemTheme

    setTheme(initialTheme)
    document.documentElement.classList.toggle("dark", initialTheme === "dark")
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark"
    setTheme(newTheme)
    localStorage.setItem("theme", newTheme)
    document.documentElement.classList.toggle("dark", newTheme === "dark")
  }

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedNotes = localStorage.getItem("notes")
    const savedTag = localStorage.getItem("selectedTag")
    const savedSort = localStorage.getItem("sortBy")

    if (savedNotes) {
      const parsedNotes = JSON.parse(savedNotes).map((note: any) => ({
        ...note,
        isFavorite: note.isFavorite || false,
        tags: note.tags || [],
        createdAt: new Date(note.createdAt),
        updatedAt: new Date(note.updatedAt),
      }))
      setNotes(parsedNotes)
    }

    if (savedTag) {
      setSelectedTag(savedTag)
    }

    if (savedSort) {
      setSortBy(savedSort as SortOption)
    }
  }, [])

  // 保存数据到 localStorage
  useEffect(() => {
    if (notes.length > 0) {
      localStorage.setItem("notes", JSON.stringify(notes))
    }
  }, [notes])

  useEffect(() => {
    localStorage.setItem("selectedTag", selectedTag)
  }, [selectedTag])

  useEffect(() => {
    localStorage.setItem("sortBy", sortBy)
  }, [sortBy])

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth >= 768) {
        setSidebarOpen(true)
      } else {
        setSidebarOpen(false)
      }
    }

    checkScreenSize()
    window.addEventListener("resize", checkScreenSize)
    return () => window.removeEventListener("resize", checkScreenSize)
  }, [])

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 删除确认对话框的键盘操作
      if (deleteConfirmation.isOpen) {
        if (e.key === "Escape") {
          setDeleteConfirmation({ isOpen: false, noteId: "", noteTitle: "" })
        } else if (e.key === "Enter") {
          confirmDeleteNote()
        }
        return
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "s":
            e.preventDefault()
            if (isEditing) {
              saveEdit()
            }
            break
          case "n":
            e.preventDefault()
            createNote()
            break
          case "f":
            e.preventDefault()
            document.querySelector<HTMLInputElement>('input[placeholder*="搜索笔记"]')?.focus()
            break
        }
      }
      if (e.key === "Escape" && isEditing) {
        cancelEdit()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [isEditing, deleteConfirmation.isOpen])

  // 自动保存功能
  const autoSave = useCallback(() => {
    if (!selectedNote || !isEditing) return

    const updatedNote: Note = {
      ...selectedNote,
      title: editTitle || "无标题",
      content: editContent,
      tags: editTags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag),
      updatedAt: new Date(),
    }

    const updatedNotes = notes.map((note) => (note.id === selectedNote.id ? updatedNote : note))
    setNotes(updatedNotes)
    setSelectedNote(updatedNote)
  }, [selectedNote, isEditing, editTitle, editContent, editTags, notes])

  // 编辑内容变化时触发自动保存
  useEffect(() => {
    if (isEditing && selectedNote) {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout)
      }
      const timeout = setTimeout(autoSave, 1000) // 1秒后自动保存
      setAutoSaveTimeout(timeout)
    }
    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout)
      }
    }
  }, [editTitle, editContent, editTags, isEditing, autoSave])

  // 获取所有标签及其使用次数
  const getAllTags = () => {
    const tagCount: { [key: string]: number } = {}
    notes.forEach((note) => {
      note.tags.forEach((tag) => {
        tagCount[tag] = (tagCount[tag] || 0) + 1
      })
    })
    return Object.entries(tagCount)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
  }

  // 创建新笔记
  const createNote = () => {
    const now = new Date()
    const newNote: Note = {
      id: Date.now().toString(),
      title: "新笔记",
      content: "",
      tags: [],
      isFavorite: false,
      createdAt: now,
      updatedAt: now,
    }
    setNotes([newNote, ...notes])
    setSelectedNote(newNote)
    setIsEditing(true)
    setEditTitle(newNote.title)
    setEditContent(newNote.content)
    setEditTags("")
    // 创建新笔记时在移动端关闭侧栏
    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  // 显示删除确认对话框
  const showDeleteConfirmation = (noteId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
    }

    const noteToDelete = notes.find((note) => note.id === noteId)
    const noteTitle = noteToDelete?.title || "此笔记"

    setDeleteConfirmation({
      isOpen: true,
      noteId,
      noteTitle,
    })
  }

  // 确认删除笔记
  const confirmDeleteNote = () => {
    const { noteId } = deleteConfirmation
    const updatedNotes = notes.filter((note) => note.id !== noteId)
    setNotes(updatedNotes)

    if (selectedNote?.id === noteId) {
      setSelectedNote(null)
      setIsEditing(false)
    }

    setDeleteConfirmation({ isOpen: false, noteId: "", noteTitle: "" })
  }

  // 取消删除
  const cancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, noteId: "", noteTitle: "" })
  }

  // 切换收藏状态
  const toggleFavorite = (noteId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
    }
    const updatedNotes = notes.map((note) =>
      note.id === noteId ? { ...note, isFavorite: !note.isFavorite, updatedAt: new Date() } : note,
    )
    setNotes(updatedNotes)

    if (selectedNote?.id === noteId) {
      setSelectedNote({ ...selectedNote, isFavorite: !selectedNote.isFavorite })
    }
  }

  // 双击编辑
  const handleDoubleClick = (note: Note) => {
    // 如果当前正在编辑其他笔记，先保存
    if (isEditing && selectedNote && selectedNote.id !== note.id) {
      autoSave()
    }

    setSelectedNote(note)
    setIsEditing(true)
    setEditTitle(note.title)
    setEditContent(note.content)
    setEditTags(note.tags.join(", "))
    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  // 开始编辑
  const startEditing = (note: Note) => {
    // 如果当前正在编辑其他笔记，先保存
    if (isEditing && selectedNote && selectedNote.id !== note.id) {
      autoSave()
    }

    setSelectedNote(note)
    setIsEditing(true)
    setEditTitle(note.title)
    setEditContent(note.content)
    setEditTags(note.tags.join(", "))
  }

  // 检查是否为空笔记
  const isEmptyNote = (title: string, content: string) => {
    return !title.trim() && !content.trim()
  }

  // 保存编辑
  const saveEdit = () => {
    if (!selectedNote) return

    // 如果是空笔记，删除它
    if (isEmptyNote(editTitle, editContent)) {
      const updatedNotes = notes.filter((note) => note.id !== selectedNote.id)
      setNotes(updatedNotes)
      setSelectedNote(null)
      setIsEditing(false)
      return
    }

    const updatedNote: Note = {
      ...selectedNote,
      title: editTitle || "无标题",
      content: editContent,
      tags: editTags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag),
      updatedAt: new Date(),
    }

    const updatedNotes = notes.map((note) => (note.id === selectedNote.id ? updatedNote : note))
    setNotes(updatedNotes)
    setSelectedNote(updatedNote)
    setIsEditing(false)
  }

  // 取消编辑
  const cancelEdit = () => {
    if (!selectedNote) return

    // 如果是新创建的空笔记，删除它
    if (isEmptyNote(editTitle, editContent) && isEmptyNote(selectedNote.title, selectedNote.content)) {
      const updatedNotes = notes.filter((note) => note.id !== selectedNote.id)
      setNotes(updatedNotes)
      setSelectedNote(null)
    } else {
      // 恢复原始内容
      setEditTitle(selectedNote.title)
      setEditContent(selectedNote.content)
      setEditTags(selectedNote.tags.join(", "))
    }
    setIsEditing(false)
  }

  // 排序笔记
  const sortNotes = (notes: Note[], sortBy: SortOption) => {
    return [...notes].sort((a, b) => {
      // 收藏的笔记总是排在前面
      if (a.isFavorite && !b.isFavorite) return -1
      if (!a.isFavorite && b.isFavorite) return 1

      switch (sortBy) {
        case "updated":
          return b.updatedAt.getTime() - a.updatedAt.getTime()
        case "created":
          return b.createdAt.getTime() - a.createdAt.getTime()
        case "title":
          return a.title.localeCompare(b.title, "zh-CN")
        case "favorite":
          if (a.isFavorite && b.isFavorite) {
            return b.updatedAt.getTime() - a.updatedAt.getTime()
          }
          return 0
        default:
          return b.updatedAt.getTime() - a.updatedAt.getTime()
      }
    })
  }

  // 过滤和排序笔记
  const filteredAndSortedNotes = useMemo(() => {
    let filtered = notes.filter((note) => {
      if (selectedTag === "all") return true
      if (selectedTag === "favorites") return note.isFavorite
      if (selectedTag === "untagged") return note.tags.length === 0
      return note.tags.includes(selectedTag)
    })

    if (searchTerm) {
      filtered = filtered.filter(
        (note) =>
          note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
          note.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }

    return sortNotes(filtered, sortBy)
  }, [notes, selectedTag, searchTerm, sortBy])

  // 格式化日期
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // 格式化日期（移动端简化版）
  const formatDateMobile = (date: Date) => {
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      })
    } else if (diffDays === 1) {
      return "昨天"
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString("zh-CN", {
        month: "short",
        day: "numeric",
      })
    }
  }

  // 选择笔记 - 只有这个函数会在移动端关闭侧栏
  const selectNote = (note: Note) => {
    // 如果当前正在编辑，先保存当前编辑的内容
    if (isEditing && selectedNote) {
      // 自动保存当前编辑的笔记
      autoSave()
    }

    // 切换到新笔记并重置编辑状态
    setSelectedNote(note)
    setIsEditing(false)

    // 重置编辑器状态为新笔记的内容
    setEditTitle(note.title)
    setEditContent(note.content)
    setEditTags(note.tags.join(", "))

    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  // 处理标签点击 - 不关闭侧栏
  const handleTagClick = (tag: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
    }
    setSelectedTag(tag)
  }

  // 高亮搜索关键词
  const highlightText = (text: string, highlight: string) => {
    if (!highlight) return text
    const parts = text.split(new RegExp(`(${highlight})`, "gi"))
    return parts.map((part, index) =>
      part.toLowerCase() === highlight.toLowerCase() ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-0.5 rounded">
          {part}
        </mark>
      ) : (
        part
      ),
    )
  }

  // 复制代码到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCode(text)
      setTimeout(() => setCopiedCode(null), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  // 更简单可靠的语法高亮函数
  const escapeHtml = (text: string) => {
    const div = document.createElement("div")
    div.textContent = text
    return div.innerHTML
  }

  // 更简单可靠的语法高亮函数
  const highlightSyntax = (code: string, language: string) => {
    // 转义 HTML 特殊字符
    const escapeHtml = (text: string) => {
      const div = document.createElement("div")
      div.textContent = text
      return div.innerHTML
    }

    const languageConfig = {
      javascript: {
        keywords: [
          "const",
          "let",
          "var",
          "function",
          "return",
          "if",
          "else",
          "for",
          "while",
          "class",
          "import",
          "export",
          "async",
          "await",
          "try",
          "catch",
          "finally",
          "throw",
          "new",
          "this",
          "super",
          "extends",
          "static",
        ],
        types: ["String", "Number", "Boolean", "Array", "Object", "Date", "Promise"],
        builtins: ["console", "window", "document", "localStorage", "JSON", "Math", "setTimeout", "setInterval"],
      },
      typescript: {
        keywords: [
          "const",
          "let",
          "var",
          "function",
          "return",
          "if",
          "else",
          "for",
          "while",
          "class",
          "import",
          "export",
          "async",
          "await",
          "try",
          "catch",
          "finally",
          "throw",
          "new",
          "this",
          "super",
          "extends",
          "static",
          "interface",
          "type",
          "enum",
          "private",
          "public",
          "protected",
        ],
        types: [
          "string",
          "number",
          "boolean",
          "any",
          "void",
          "never",
          "unknown",
          "String",
          "Number",
          "Boolean",
          "Array",
          "Promise",
        ],
        builtins: ["console", "window", "document", "localStorage", "JSON", "Math", "setTimeout", "setInterval"],
      },
      python: {
        keywords: [
          "def",
          "class",
          "import",
          "from",
          "return",
          "if",
          "else",
          "elif",
          "for",
          "while",
          "try",
          "except",
          "finally",
          "with",
          "as",
          "pass",
          "break",
          "continue",
          "and",
          "or",
          "not",
          "in",
          "is",
          "lambda",
          "yield",
        ],
        types: ["int", "float", "str", "bool", "list", "dict", "tuple", "set", "None", "True", "False"],
        builtins: [
          "print",
          "len",
          "range",
          "enumerate",
          "zip",
          "map",
          "filter",
          "sorted",
          "sum",
          "max",
          "min",
          "abs",
          "round",
          "type",
          "isinstance",
        ],
      },
      java: {
        keywords: [
          "public",
          "private",
          "protected",
          "static",
          "final",
          "abstract",
          "class",
          "interface",
          "extends",
          "implements",
          "return",
          "if",
          "else",
          "for",
          "while",
          "do",
          "switch",
          "case",
          "default",
          "break",
          "continue",
          "try",
          "catch",
          "finally",
          "throw",
          "throws",
          "new",
          "this",
          "super",
          "package",
          "import",
        ],
        types: [
          "int",
          "long",
          "short",
          "byte",
          "float",
          "double",
          "boolean",
          "char",
          "String",
          "Integer",
          "Long",
          "Double",
          "Boolean",
          "Object",
          "List",
          "Map",
          "Set",
        ],
        builtins: ["System", "Math", "String", "StringBuilder", "ArrayList", "HashMap"],
      },
      css: {
        keywords: ["@media", "@import", "@keyframes", "@font-face", "!important"],
        types: [],
        builtins: [
          "color",
          "background",
          "margin",
          "padding",
          "border",
          "width",
          "height",
          "display",
          "position",
          "flex",
          "grid",
          "font-size",
          "font-family",
        ],
      },
    }

    const config = languageConfig[language as keyof typeof languageConfig]
    if (!config) {
      return escapeHtml(code)
    }

    // 先转义 HTML
    let result = escapeHtml(code)

    // 高亮字符串（优先处理，避免字符串内的关键词被高亮）
    result = result.replace(/(["'`])([^"'`]*?)\1/g, '<span style="color: #4ADE80;">$1$2$1</span>')

    // 高亮注释
    result = result.replace(
      /(\/\/.*$|\/\*[\s\S]*?\*\/|#.*$)/gm,
      '<span style="color: #9CA3AF; font-style: italic;">$1</span>',
    )

    // 高亮数字
    result = result.replace(/\b(\d+\.?\d*)\b/g, '<span style="color: #FB923C;">$1</span>')

    // 高亮关键词
    config.keywords.forEach((keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, "g")
      result = result.replace(regex, `<span style="color: #60A5FA; font-weight: 600;">${keyword}</span>`)
    })

    // 高亮类型
    config.types.forEach((type) => {
      const regex = new RegExp(`\\b${type}\\b`, "g")
      result = result.replace(regex, `<span style="color: #22D3EE; font-weight: 500;">${type}</span>`)
    })

    // 高亮内置函数
    config.builtins.forEach((builtin) => {
      const regex = new RegExp(`\\b${builtin}\\b`, "g")
      result = result.replace(regex, `<span style="color: #A78BFA;">${builtin}</span>`)
    })

    return result
  }

  // 添加行号功能
  const addLineNumbers = (code: string) => {
    const lines = code.split("\n")
    return lines.map((line, index) => ({
      number: index + 1,
      content: line,
    }))
  }

  // 渲染带有代码高亮的内容
  const renderContentWithCodeHighlight = (content: string) => {
    const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g
    const inlineCodeRegex = /`([^`]+)`/g

    const elements: React.ReactNode[] = []

    // 处理代码块
    const contentWithCodeBlocks = content.replace(codeBlockRegex, (match, language, code, offset) => {
      const placeholder = `__CODE_BLOCK_${elements.length}__`
      const trimmedCode = code.trim()
      const linesWithNumbers = addLineNumbers(trimmedCode)
      const maxLineNumber = linesWithNumbers.length
      const lineNumberWidth = maxLineNumber.toString().length

      elements.push(
        <div
          key={elements.length}
          className="relative group my-6 rounded-lg overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700"
        >
          {/* 代码块头部 */}
          <div className="flex items-center justify-between bg-gradient-to-r from-gray-800 to-gray-900 dark:from-gray-900 dark:to-black text-gray-200 px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="flex gap-1.5">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <span className="font-mono text-sm font-medium">{language ? language.toUpperCase() : "CODE"}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(trimmedCode)}
              className="h-7 w-7 p-0 text-gray-400 hover:text-gray-200 hover:bg-gray-700 transition-all duration-200"
              title="复制代码"
            >
              {copiedCode === trimmedCode ? (
                <Check className="h-3.5 w-3.5 text-green-400" />
              ) : (
                <Copy className="h-3.5 w-3.5" />
              )}
            </Button>
          </div>

          {/* 代码内容 */}
          <div className="bg-gray-900 dark:bg-gray-900 text-gray-100 overflow-x-auto">
            <div className="flex">
              {/* 行号 */}
              <div className="bg-gray-800 dark:bg-gray-800 text-gray-400 text-right py-4 px-3 select-none border-r border-gray-600 dark:border-gray-600">
                {linesWithNumbers.map((line) => (
                  <div
                    key={line.number}
                    className="font-mono text-xs leading-6 hover:text-gray-300 transition-colors"
                    style={{ minWidth: `${lineNumberWidth * 0.6 + 0.5}rem` }}
                  >
                    {line.number}
                  </div>
                ))}
              </div>

              {/* 代码内容 */}
              <div className="flex-1 py-4 px-4">
                {linesWithNumbers.map((line, index) => (
                  <div
                    key={index}
                    className="font-mono text-sm leading-6 hover:bg-gray-800/50 dark:hover:bg-gray-800/50 px-2 -mx-2 rounded transition-colors"
                  >
                    {line.content ? (
                      <code
                        style={{ color: "#E5E7EB" }}
                        dangerouslySetInnerHTML={{
                          __html: language ? highlightSyntax(line.content, language) : escapeHtml(line.content),
                        }}
                      />
                    ) : (
                      <span className="opacity-50">​</span> // 空行占位符
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>,
      )

      return placeholder
    })

    // 处理行内代码和普通文本
    const parts = contentWithCodeBlocks.split(/(__CODE_BLOCK_\d+__)/g)

    return parts.map((part, index) => {
      if (part.startsWith("__CODE_BLOCK_")) {
        const blockIndex = Number.parseInt(part.match(/\d+/)?.[0] || "0")
        return elements[blockIndex]
      }

      // 处理行内代码
      const inlineParts = part.split(inlineCodeRegex)
      return inlineParts.map((inlinePart, inlineIndex) => {
        if (inlineIndex % 2 === 1) {
          return (
            <code
              key={`${index}-${inlineIndex}`}
              className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-1 rounded-md text-sm font-mono border border-gray-200 dark:border-gray-700 shadow-sm"
            >
              {inlinePart}
            </code>
          )
        }

        return inlinePart.split("\n").map((line, lineIndex, lines) => (
          <React.Fragment key={`${index}-${inlineIndex}-${lineIndex}`}>
            {line}
            {lineIndex < lines.length - 1 && <br />}
          </React.Fragment>
        ))
      })
    })
  }

  // 获取筛选选项
  const getFilterOptions = () => {
    const options = [
      { value: "all", label: `全部笔记 (${notes.length})`, icon: "📝" },
      { value: "favorites", label: `收藏 (${notes.filter((n) => n.isFavorite).length})`, icon: "⭐" },
      { value: "untagged", label: `无标签 (${notes.filter((n) => n.tags.length === 0).length})`, icon: "📄" },
    ]

    getAllTags().forEach(({ tag, count }) => {
      options.push({
        value: tag,
        label: `${tag} (${count})`,
        icon: "🏷️",
      })
    })

    return options
  }

  // 获取排序选项
  const getSortOptions = () => [
    { value: "updated", label: "最近更新", icon: Clock },
    { value: "created", label: "创建时间", icon: Calendar },
    { value: "title", label: "标题排序", icon: AlphabeticalIcon },
    { value: "favorite", label: "收藏优先", icon: Heart },
  ]

  // 计算字数统计
  const getWordCount = (text: string) => {
    const chars = text.length
    const words = text.trim() ? text.trim().split(/\s+/).length : 0
    const lines = text.split("\n").length
    return { chars, words, lines }
  }

  const currentWordCount = useMemo(() => {
    if (isEditing) {
      return getWordCount(editContent)
    } else if (selectedNote) {
      return getWordCount(selectedNote.content)
    }
    return { chars: 0, words: 0, lines: 0 }
  }, [isEditing, editContent, selectedNote])

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-background relative overflow-hidden">
      {/* 自定义标题栏 - 只在 Tauri 中显示 */}
      <SimpleTitlebar />

      <div className="flex flex-1 min-h-0 overflow-hidden">
      {/* 删除确认对话框 */}
      {deleteConfirmation.isOpen && (
        <div className="fixed inset-0 bg-black/50 z-[100] flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 w-full max-w-md mx-auto animate-in fade-in-0 zoom-in-95 duration-200">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">确认删除笔记</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">此操作无法撤销</p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 dark:text-gray-300">
                  确定要删除笔记{" "}
                  <span className="font-medium text-gray-900 dark:text-gray-100">"{deleteConfirmation.noteTitle}"</span>{" "}
                  吗？
                </p>
              </div>

              <div className="flex gap-3 justify-end">
                <Button
                  variant="outline"
                  onClick={cancelDelete}
                  size="sm"
                  className="border-gray-200 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 bg-transparent dark:bg-transparent"
                >
                  取消
                </Button>
                <Button
                  onClick={confirmDeleteNote}
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white dark:bg-red-600 dark:hover:bg-red-700"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 移动端遮罩层 */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={() => setSidebarOpen(false)} />
      )}

      {/* 侧边栏 */}
      <div
        className={`
        ${isMobile ? "fixed" : "relative"}
        w-80 border-r border-gray-200 dark:border-border bg-white dark:bg-background flex flex-col z-50
        transition-transform duration-300 ease-in-out overflow-hidden
        ${isMobile && !sidebarOpen ? "-translate-x-full" : "translate-x-0"}
        h-full
      `}
      >
        {/* 头部 */}
        <div className="p-4 border-b border-gray-200 dark:border-border bg-gray-50 dark:bg-background">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-foreground">简单笔记</h1>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
                title="切换主题"
                className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800"
              >
                {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </Button>
              <Button
                onClick={createNote}
                size="sm"
                title="新建笔记 (Ctrl+N)"
                className="bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100"
              >
                <Plus className="h-4 w-4 mr-1" />
                新建
              </Button>
            </div>
          </div>

          {/* 搜索框 */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-muted-foreground" />
            <Input
              placeholder="搜索笔记... (Ctrl+F)"
              value={searchInput}
              onChange={(e) => handleSearchInput(e.target.value)}
              className="pl-10 bg-white dark:bg-background border-gray-200 dark:border-border text-gray-700 dark:text-foreground placeholder:text-gray-400 dark:placeholder:text-muted-foreground focus:border-gray-500 dark:focus:border-gray-400 focus:ring-0"
            />
          </div>

          {/* 筛选和排序 */}
          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500 dark:text-muted-foreground" />
                <span className="text-sm font-medium text-gray-700 dark:text-foreground">筛选</span>
              </div>
              <Select value={selectedTag} onValueChange={setSelectedTag}>
                <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600">
                  {getFilterOptions().map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700"
                    >
                      <div className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <ArrowUpDown className="h-4 w-4 text-gray-500 dark:text-muted-foreground" />
                <span className="text-sm font-medium text-gray-700 dark:text-foreground">排序</span>
              </div>
              <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600">
                  {getSortOptions().map((option) => {
                    const IconComponent = option.icon
                    return (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                        className="text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700"
                      >
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 笔记列表 */}
        <ScrollArea className="flex-1 bg-white dark:bg-background">
          <div className="p-2">
            {filteredAndSortedNotes.length === 0 ? (
              <div className="text-center text-gray-500 dark:text-muted-foreground py-8">
                {searchTerm ? "未找到匹配的笔记" : "还没有笔记，创建第一个吧！"}
              </div>
            ) : (
              filteredAndSortedNotes.map((note) => (
                <Card
                  key={note.id}
                  className={`mb-2 cursor-pointer transition-all duration-200 group border-gray-200 dark:border-border bg-white dark:bg-card hover:bg-gray-50 dark:hover:bg-muted/50 hover:shadow-sm ${
                    selectedNote?.id === note.id ? "ring-2 ring-gray-400 dark:ring-primary shadow-sm" : ""
                  }`}
                  onClick={() => selectNote(note)}
                  onDoubleClick={() => handleDoubleClick(note)}
                  title="双击编辑"
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <CardTitle className="text-sm font-medium truncate flex-1 text-gray-900 dark:text-card-foreground">
                          {highlightText(note.title, searchTerm)}
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => toggleFavorite(note.id, e)}
                          className={`h-6 w-6 p-0 transition-all duration-200 ${
                            note.isFavorite
                              ? "text-amber-500 hover:text-amber-600"
                              : `text-gray-400 hover:text-amber-500 dark:text-muted-foreground dark:hover:text-amber-500 ${
                                  isMobile ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                                }`
                          }`}
                        >
                          <Star className={`h-3 w-3 ${note.isFavorite ? "fill-current" : ""}`} />
                        </Button>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => showDeleteConfirmation(note.id, e)}
                        className={`h-6 w-6 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:text-muted-foreground dark:hover:text-destructive dark:hover:bg-destructive/10 transition-all duration-200 ${
                          isMobile ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                        }`}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs text-gray-500 dark:text-muted-foreground line-clamp-2 mb-2">
                      {note.content ? highlightText(note.content, searchTerm) : "无内容"}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {note.tags.slice(0, 3).map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs cursor-pointer transition-colors duration-200 bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900 dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-muted dark:hover:text-foreground"
                            onClick={(e) => handleTagClick(tag, e)}
                          >
                            {highlightText(tag, searchTerm)}
                          </Badge>
                        ))}
                        {note.tags.length > 3 && (
                          <Badge
                            variant="secondary"
                            className="text-xs bg-gray-100 text-gray-600 dark:bg-secondary dark:text-secondary-foreground"
                          >
                            +{note.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-gray-400 dark:text-muted-foreground">
                        {formatDate(note.updatedAt)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>

        {/* 开发环境调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-4 border-t border-gray-200 dark:border-border">
            <TauriDebug />
          </div>
        )}
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col min-w-0 bg-white dark:bg-background">
        {selectedNote ? (
          <>
            {/* 笔记头部 */}
            <div className="p-4 border-b border-gray-200 dark:border-border bg-gray-50 dark:bg-background">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  {isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSidebarOpen(true)}
                      className="flex-shrink-0 text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800"
                    >
                      <Menu className="h-4 w-4" />
                    </Button>
                  )}
                  <div className="flex items-center gap-2 min-w-0">
                    <Calendar className="h-4 w-4 text-gray-500 dark:text-muted-foreground flex-shrink-0" />
                    {isMobile ? (
                      <div className="flex flex-col min-w-0">
                        <span className="text-xs text-gray-500 dark:text-muted-foreground truncate">
                          创建 {formatDateMobile(selectedNote.createdAt)}
                        </span>
                        {selectedNote.updatedAt.getTime() !== selectedNote.createdAt.getTime() && (
                          <span className="text-xs text-gray-500 dark:text-muted-foreground truncate">
                            更新 {formatDateMobile(selectedNote.updatedAt)}
                          </span>
                        )}
                      </div>
                    ) : (
                      <>
                        <span className="text-sm text-gray-500 dark:text-muted-foreground truncate">
                          创建于 {formatDate(selectedNote.createdAt)}
                        </span>
                        {selectedNote.updatedAt.getTime() !== selectedNote.createdAt.getTime() && (
                          <>
                            <span className="text-gray-400 dark:text-muted-foreground">•</span>
                            <span className="text-sm text-gray-500 dark:text-muted-foreground truncate">
                              更新于 {formatDate(selectedNote.updatedAt)}
                            </span>
                          </>
                        )}
                      </>
                    )}
                  </div>
                </div>
                <div className="flex gap-2 flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleFavorite(selectedNote.id)}
                    className={`transition-colors duration-200 ${
                      selectedNote.isFavorite
                        ? "text-amber-500 hover:text-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/20"
                        : "text-gray-400 hover:text-amber-500 hover:bg-amber-50 dark:text-gray-500 dark:hover:text-amber-400 dark:hover:bg-amber-900/20"
                    }`}
                  >
                    <Star className={`h-4 w-4 ${selectedNote.isFavorite ? "fill-current" : ""}`} />
                  </Button>
                  {isEditing ? (
                    <>
                      <Button
                        onClick={saveEdit}
                        size="sm"
                        title="保存 (Ctrl+S)"
                        className="bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100"
                      >
                        保存
                      </Button>
                      <Button
                        onClick={cancelEdit}
                        variant="outline"
                        size="sm"
                        title="取消 (Esc)"
                        className="border-gray-200 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 bg-transparent dark:bg-transparent"
                      >
                        取消
                      </Button>
                    </>
                  ) : (
                    <Button
                      onClick={() => startEditing(selectedNote)}
                      size="sm"
                      className="bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100"
                    >
                      <Edit3 className="h-4 w-4 mr-1" />
                      编辑
                    </Button>
                  )}
                </div>
              </div>

              {/* 标签显示 */}
              {selectedNote.tags.length > 0 && !isEditing && (
                <div className="flex items-center gap-2 mb-2">
                  <Hash className="h-4 w-4 text-gray-500 dark:text-muted-foreground flex-shrink-0" />
                  <div className="flex flex-wrap gap-1">
                    {selectedNote.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="cursor-pointer transition-colors duration-200 bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900 dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-muted dark:hover:text-foreground"
                        onClick={() => handleTagClick(tag)}
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 字数统计和自动保存提示 */}
              <div className="flex items-center justify-between">
                {/* 字数统计 */}
                <div className="text-xs text-gray-500 dark:text-muted-foreground">
                  {currentWordCount.chars} 字符 • {currentWordCount.words} 词 • {currentWordCount.lines} 行
                </div>

                {/* 自动保存提示 */}
                {isEditing && (
                  <div className="text-xs text-gray-600 dark:text-muted-foreground bg-gray-100 dark:bg-muted/50 px-2 py-1 rounded">
                    💾 自动保存 • Ctrl+S 保存 • Esc 取消
                  </div>
                )}
              </div>
            </div>

            {/* 笔记内容 */}
            <div className="flex-1 p-4 overflow-auto bg-white dark:bg-background">
              {isEditing ? (
                <div className="flex flex-col h-full gap-4">
                  <Input
                    placeholder="笔记标题"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    className="text-lg font-medium bg-white dark:bg-background border-gray-200 dark:border-border text-gray-900 dark:text-foreground placeholder:text-gray-400 dark:placeholder:text-muted-foreground focus:border-gray-500 dark:focus:border-gray-400 focus:ring-0"
                  />
                  <div className="relative">
                    <Input
                      placeholder="标签 (用逗号分隔，如：工作, 学习, 重要)"
                      value={editTags}
                      onChange={(e) => setEditTags(e.target.value)}
                      className="bg-white dark:bg-background border-gray-200 dark:border-border text-gray-700 dark:text-foreground placeholder:text-gray-400 dark:placeholder:text-muted-foreground focus:border-gray-500 dark:focus:border-gray-400 focus:ring-0"
                    />
                    <Tag className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-muted-foreground" />
                  </div>
                  <div className="flex-1 min-h-0 border border-gray-200 dark:border-border rounded-md bg-white dark:bg-background focus-within:border-gray-500 dark:focus-within:border-gray-400 transition-colors overflow-hidden">
                    <ScrollArea className="h-full w-full">
                      <div className="p-3 min-h-full">
                        <textarea
                          placeholder={`开始写笔记...
💡 支持代码高亮：
- 代码块：\`\`\`javascript 或 \`\`\`python 或 \`\`\`typescript
- 行内代码：\`代码\`
- 支持语言：JavaScript, TypeScript, Python, Java, CSS`}
                          value={editContent}
                          onChange={(e) => setEditContent(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Tab') {
                              e.preventDefault()
                              const textarea = e.target as HTMLTextAreaElement
                              const start = textarea.selectionStart
                              const end = textarea.selectionEnd
                              const newValue = editContent.substring(0, start) + '\t' + editContent.substring(end)
                              setEditContent(newValue)
                              // 设置光标位置到插入的制表符之后
                              setTimeout(() => {
                                textarea.selectionStart = textarea.selectionEnd = start + 1
                              }, 0)
                            }
                          }}
                          className="w-full resize-none border-0 bg-transparent text-gray-700 dark:text-foreground placeholder:text-gray-400 dark:placeholder:text-muted-foreground focus:ring-0 focus:outline-none p-0 shadow-none overflow-hidden"
                          style={{
                            height: `${Math.max(300, editContent.split('\n').length * 24 + 100)}px`,
                            lineHeight: '24px',
                            minHeight: '100%'
                          }}
                        />
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              ) : (
                <div className="h-full">
                  <h1 className="text-xl md:text-2xl font-bold mb-4 break-words text-gray-900 dark:text-foreground">
                    {selectedNote.title}
                  </h1>
                  <div className="prose prose-sm max-w-none">
                    {selectedNote.content ? (
                      <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed break-words text-gray-600 dark:text-muted-foreground bg-gray-50 dark:bg-muted/30 p-4 rounded-lg border border-gray-200 dark:border-border">
                        {renderContentWithCodeHighlight(selectedNote.content)}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-muted-foreground italic">这个笔记还没有内容</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4 bg-white dark:bg-background">
            <div className="text-center">
              {isMobile && (
                <Button
                  variant="outline"
                  onClick={() => setSidebarOpen(true)}
                  className="mb-4 border-gray-200 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800"
                >
                  <Menu className="h-4 w-4 mr-2" />
                  打开笔记列表
                </Button>
              )}
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-muted flex items-center justify-center">
                <Edit3 className="h-8 w-8 text-gray-400 dark:text-muted-foreground" />
              </div>
              <h2 className="text-lg md:text-xl font-semibold mb-2 text-gray-900 dark:text-foreground">
                选择一个笔记开始编辑
              </h2>
              <p className="text-gray-500 dark:text-muted-foreground mb-4 text-sm md:text-base">
                {isMobile ? "点击上方按钮打开笔记列表" : "从左侧选择一个笔记，或者创建一个新的笔记"}
              </p>
              <div className="text-xs text-gray-400 dark:text-muted-foreground mb-4 bg-gray-50 dark:bg-muted/50 px-3 py-2 rounded-lg">
                💡 提示：双击笔记快速编辑 • 快捷键：Ctrl+N 新建，Ctrl+F 搜索 • 支持代码高亮
              </div>
              <Button
                onClick={createNote}
                className="bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100"
              >
                <Plus className="h-4 w-4 mr-2" />
                创建新笔记
              </Button>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}
