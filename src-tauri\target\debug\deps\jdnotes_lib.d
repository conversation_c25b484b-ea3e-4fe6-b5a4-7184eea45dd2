E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\jdnotes_lib.d: src\lib.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\jdnotes-f8d5de7503eac8dc\out/e38b26c1f5ae3765617b2d454b4981c78030df6a4319687cf0910c3702e48631

E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\jdnotes_lib.lib: src\lib.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\jdnotes-f8d5de7503eac8dc\out/e38b26c1f5ae3765617b2d454b4981c78030df6a4319687cf0910c3702e48631

E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\jdnotes_lib.dll: src\lib.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\jdnotes-f8d5de7503eac8dc\out/e38b26c1f5ae3765617b2d454b4981c78030df6a4319687cf0910c3702e48631

E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\libjdnotes_lib.rlib: src\lib.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\jdnotes-f8d5de7503eac8dc\out/e38b26c1f5ae3765617b2d454b4981c78030df6a4319687cf0910c3702e48631

src\lib.rs:
E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\jdnotes-f8d5de7503eac8dc\out/e38b26c1f5ae3765617b2d454b4981c78030df6a4319687cf0910c3702e48631:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A jiandan note app
# env-dep:CARGO_PKG_NAME=jdnotes
# env-dep:OUT_DIR=E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\jdnotes-f8d5de7503eac8dc\\out
